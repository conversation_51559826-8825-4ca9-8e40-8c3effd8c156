# Tobacco Sales Excel Report Module

This Odoo 13 CE module provides comprehensive monthly Excel reporting for tobacco and cigar sales with detailed customer information and regulatory compliance data.

## Features

- **Dynamic Excel Reports**: Generate detailed Excel spreadsheets with customizable product type filtering
- **Multiple Customer Selection**: Choose specific customers or include all customers
- **Product Type Filtering**: Generate reports for Tobacco Only, Cigars Only, or Both
- **Dynamic Report Titles**: Report names and titles automatically change based on selected product type
- **Customer Information**: Include customer names, federal IDs, and complete addresses
- **Sales Details**: Track sale dates, invoice dates, invoice numbers, and amounts
- **Manual Stick Count Entry**: Salespersons can manually enter the actual number of individual cigar sticks being sold
- **Quantity vs Stick Count**: Distinguishes between UOM quantity and actual individual cigars sold
- **Compliance Ready**: Designed for regulatory reporting requirements

## Dynamic Report Types

### Tobacco Only Report
**Title**: "Monthly Tobacco Sales Report"
**Filename**: `tobacco_sales_report_YYYY-MM-DD_YYYY-MM-DD.xlsx`
**Columns**: Customer Name, Federal ID, Sale Date, Invoice Date, Invoice Number, Customer Address, Total Tobacco Sales, Total Ounces

### Cigars Only Report
**Title**: "Monthly Cigar Sales Report"
**Filename**: `cigar_sales_report_YYYY-MM-DD_YYYY-MM-DD.xlsx`
**Columns**: Customer Name, Federal ID, Sale Date, Invoice Date, Invoice Number, Customer Address, Cigar Quantity, Premium Cigar Stick Count, Premium Cigar Amount

### Both Tobacco & Cigars Report
**Title**: "Monthly Tobacco & Cigar Sales Report"
**Filename**: `tobacco_cigar_sales_report_YYYY-MM-DD_YYYY-MM-DD.xlsx`
**Columns**: Customer Name, Federal ID, Sale Date, Invoice Date, Invoice Number, Customer Address, Total Tobacco Sales, Cigar Quantity, Premium Cigar Stick Count, Premium Cigar Amount, Total Ounces

## Common Report Fields

1. **Customer Name** - Full customer name from invoice
2. **Federal ID** - Customer's federal tax ID (stored in VAT field)
3. **Sale Date** - Date of the sale
4. **Invoice Date** - Date of the invoice
5. **Invoice Number** - Invoice reference number
6. **Customer Address** - Complete formatted customer address

## Product-Specific Fields

- **Total Tobacco Sales** - Total amount for tobacco products
- **Cigar Quantity** - Quantity sold (UOM units)
- **Premium Cigar Stick Count** - **MANUAL ENTRY** - Actual individual cigars sold
- **Premium Cigar Amount** - Total amount for cigar sales
- **Total Ounces** - Total ounces (placeholder for pipe tobacco - to be implemented)

## Prerequisites

1. **Odoo 13 CE** installed and running
2. **ai_ci_to_invoice module** installed (for tobacco/cigar classification)
3. **xlsxwriter Python library** installed:
   ```bash
   pip install xlsxwriter
   ```

## Installation

1. Copy the `tobacco_sales_report` folder to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Tobacco Sales Excel Report" module
4. The module will automatically create menu items under:
   - Sales > Tobacco Reports > Monthly Sales Report
   - Reporting > Tobacco Reports > Monthly Tobacco Sales

## Usage

### Setting Up Stick Count (Important!)

1. **For each cigar product sale**, the salesperson should manually enter the **Cigar Stick Count** field
2. This field appears in:
   - **Sale Order Lines** - Enter the actual number of individual cigars being sold
   - **Invoice Lines** - The stick count carries over from the sale order
3. **Example Scenarios**:
   - **Selling partial boxes**: 2 boxes (20 sticks each) but only selling 35 individual sticks:
     - **Quantity**: 2 (boxes)
     - **Cigar Stick Count**: 35 (actual individual cigars)
   - **Selling individual cigars**: Customer buys 5 individual cigars from different boxes:
     - **Quantity**: 0.25 (partial box)
     - **Cigar Stick Count**: 5 (actual individual cigars)
   - **Mixed box sizes**: 1 box of 10 + 1 box of 20, selling all:
     - **Quantity**: 2 (boxes)
     - **Cigar Stick Count**: 30 (actual individual cigars)

### Auto-Calculation Feature

The stick count field has smart auto-calculation:
1. **When you select a cigar product**, it tries to auto-calculate based on:
   - Product custom fields: `x_sticks_per_unit`, `x_stick_count`, `x_pieces_per_box`, `x_cigars_per_box`
   - Falls back to quantity × 1 if no custom field is found
2. **Manual override**: You can always change the auto-calculated value
3. **Carries over**: Stick count transfers from sale orders to invoices automatically

### Generating Reports

1. Navigate to **Sales > Tobacco Reports > Monthly Sales Report**
2. **Select date range** for your report
3. **Choose product type**:
   - **Both Tobacco and Cigars** (default) - Complete report with all products
   - **Tobacco Only** - Only tobacco products, excludes cigars
   - **Cigars Only** - Only cigar products, excludes tobacco
4. **Select customers** (optional - leave empty for all customers)
5. Click **"Preview Data"** to see how many records will be included
6. Click **"Generate Excel Report"** to create and download the Excel file
7. The Excel file will be automatically downloaded with a dynamic filename based on your selection:
   - `tobacco_sales_report_2024-01-01_2024-01-31.xlsx` (Tobacco Only)
   - `cigar_sales_report_2024-01-01_2024-01-31.xlsx` (Cigars Only)
   - `tobacco_cigar_sales_report_2024-01-01_2024-01-31.xlsx` (Both)

## Excel Report Format

The generated Excel file includes:
- **Dynamic title row** with product type and report period (e.g., "Monthly Cigar Sales Report (2024-01-01 to 2024-01-31)")
- **Dynamic header row** with relevant column names based on product type selection
- **Data rows** with sales information filtered by product type and customers
- **Totals row** with summary calculations for relevant columns
- **Professional formatting** with borders, colors, and proper column widths
- **Optimized columns** - only shows relevant data based on your selection

## Dependencies

- `base` - Odoo base module
- `sale` - Sales management
- `account` - Accounting/Invoicing
- `product` - Product management
- `ai_ci_to_invoice` - Tobacco/cigar classification module

## Technical Notes

- **Dynamic Report Generation**: Report structure adapts based on product type selection
- **Smart Filtering**: Only relevant columns and data are included based on your choices
- **Customer Filtering**: Supports multiple customer selection with Many2many relationship
- **Product Classification**: Uses existing tobacco/cigar classification from the `ai_ci_to_invoice` module
- **Federal ID Storage**: Federal ID is stored in the customer's VAT field
- **Data Integrity**: Only posted invoices are included in the report
- **Product Methods**: Products are classified using the `is_tobacco_category()` and `is_cigar_category()` methods
- **Excel Generation**: Uses the `xlsxwriter` library for professional formatting
- **Stick Count Logic**: Manual stick count takes precedence over calculated values

## Troubleshooting

**Error: "xlsxwriter library is required"**
- Install xlsxwriter: `pip install xlsxwriter`
- Restart Odoo service

**No data in report**
- Ensure you have posted invoices with tobacco/cigar products in the selected date range
- Check that products are properly categorized as tobacco or cigar products
- Verify the `ai_ci_to_invoice` module is installed and working

**Federal ID not showing**
- Federal ID is stored in the customer's VAT field
- Update customer records to include VAT/Federal ID information

## Support

For issues or questions, please contact your system administrator or the module developer.

## Version

- **Version**: ********.0
- **Odoo Version**: 13.0 Community Edition
- **Author**: Your Company
