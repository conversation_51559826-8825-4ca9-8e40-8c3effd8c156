# -*- coding: utf-8 -*-
"""
Tobacco Sales Report Model

This model handles the generation of Excel reports for tobacco and cigar sales
with comprehensive customer and product information.
"""

import base64
import io
from datetime import date
from odoo import models, fields, api, _
from odoo.exceptions import UserError

try:
    import xlsxwriter
except ImportError:
    xlsxwriter = None


class TobaccoSalesReport(models.TransientModel):
    """
    Model for generating tobacco sales Excel reports.
    
    This model collects sales data for tobacco and cigar products within a specified
    date range and generates a comprehensive Excel report with customer details,
    product information, and sales amounts.
    """
    _name = 'tobacco.sales.report'
    _description = 'Tobacco Sales Excel Report'

    date_from = fields.Date(
        string='From Date',
        required=True,
        default=lambda _: date.today().replace(day=1)
    )
    date_to = fields.Date(
        string='To Date',
        required=True,
        default=fields.Date.today
    )
    partner_ids = fields.Many2many(
        'res.partner',
        string='Customers',
        help="Select specific customers for the report. Leave empty to include all customers."
    )
    product_type = fields.Selection([
        ('both', 'Both Tobacco and Cigars'),
        ('tobacco', 'Tobacco Only'),
        ('cigars', 'Cigars Only'),
    ], string='Product Type', default='both', required=True,
       help="Select which product types to include in the report")
    user_id = fields.Many2one(
        'res.users',
        string='Report User',
        required=True,
        help="User to filter invoices by creator. Official reports <NAME_EMAIL> to exclude test data."
    )
    report_file = fields.Binary(
        string='Report File',
        readonly=True
    )
    report_filename = fields.Char(
        string='Report Filename',
        readonly=True
    )
    state = fields.Selection([
        ('draft', 'Draft'),
        ('generated', 'Generated')
    ], default='draft')

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        """Validate that from date is not after to date."""
        for record in self:
            if record.date_from > record.date_to:
                raise UserError(_('From Date cannot be after To Date.'))

    def generate_report(self):
        """
        Generate the tobacco sales Excel report.
        
        Returns:
            dict: Action to download the generated report
        """
        if not xlsxwriter:
            raise UserError(_('xlsxwriter library is required. Please install it using: pip install xlsxwriter'))
        
        # Collect sales data
        sales_data = self._collect_sales_data()
        
        # Generate Excel file
        excel_file = self._generate_excel_file(sales_data)

        # Generate filename based on product type
        product_type_name = {
            'both': 'tobacco_cigar_sales',
            'tobacco': 'tobacco_sales',
            'cigars': 'cigar_sales'
        }.get(self.product_type, 'tobacco_sales')

        filename = f'{product_type_name}_report_{self.date_from}_{self.date_to}.xlsx'
        self.write({
            'report_file': base64.b64encode(excel_file),
            'report_filename': filename,
            'state': 'generated'
        })
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content?model={self._name}&id={self.id}&field=report_file&download=true&filename={filename}',
            'target': 'self',
        }

    def _collect_sales_data(self):
        """
        Collect sales data for tobacco and cigar products within the date range.
        
        Returns:
            list: List of dictionaries containing sales data
        """
        # Get all invoices within the date range that contain tobacco/cigar products
        domain = [
            ('type', '=', 'out_invoice'),
            ('state', '=', 'posted'),
            ('invoice_date', '>=', self.date_from),
            ('invoice_date', '<=', self.date_to),
        ]

        # Add user filter - filter by invoice creator
        if self.user_id:
            domain.append(('create_uid', '=', self.user_id.id))

        # Add customer filter if specified
        if self.partner_ids:
            domain.append(('partner_id', 'in', self.partner_ids.ids))
        
        invoices = self.env['account.move'].search(domain)
        sales_data = []
        
        for invoice in invoices:
            # Filter lines based on product type selection
            if self.product_type == 'tobacco':
                tobacco_lines = invoice.invoice_line_ids.filtered(
                    lambda line: line.product_id and line.product_id.is_tobacco_category()
                )
            elif self.product_type == 'cigars':
                tobacco_lines = invoice.invoice_line_ids.filtered(
                    lambda line: line.product_id and line.product_id.is_cigar_category()
                )
            else:  # both
                tobacco_lines = invoice.invoice_line_ids.filtered(
                    lambda line: line.product_id and (
                        line.product_id.is_tobacco_category() or
                        line.product_id.is_cigar_category()
                    )
                )
            
            if tobacco_lines:
                # Calculate totals for this invoice - sales amount for tobacco/cigar products only
                tobacco_total = sum(
                    line.price_subtotal for line in tobacco_lines
                    if line.product_id.is_tobacco_category()
                )
                cigar_total = sum(
                    line.price_subtotal for line in tobacco_lines
                    if line.product_id.is_cigar_category()
                )

                # Calculate actual stick count for cigars using manual entry
                cigar_stick_count = 0
                cigar_quantity_sold = 0
                for line in tobacco_lines:
                    if line.product_id.is_cigar_category():
                        cigar_quantity_sold += line.quantity
                        # Use manual stick count if available, otherwise use quantity (since UOM is unit)
                        manual_stick_count = getattr(line, 'x_cigar_stick_count', 0)
                        if manual_stick_count > 0:
                            cigar_stick_count += manual_stick_count
                        else:
                            # Since UOM is unit, quantity already represents individual cigars
                            cigar_stick_count += line.quantity
                
                # Calculate total ounces (will be implemented later)
                # total_ounces = 0  # Placeholder for pipe tobacco calculation
                
                # Get the company name and address - use parent company if available, otherwise use partner
                partner = invoice.partner_id
                company_partner = partner.parent_id if partner.parent_id else partner
                customer_name = company_partner.name

                # Get federal ID - prefer parent company's VAT, fallback to contact's VAT if parent is empty
                federal_id = ''
                if company_partner.vat and company_partner.vat.strip():
                    federal_id = company_partner.vat.strip()
                elif partner.vat and partner.vat.strip():
                    federal_id = partner.vat.strip()

                sales_data.append({
                    'customer_name': customer_name or '',
                    'federal_id': federal_id,  # Using VAT field for Federal ID from company or contact
                    'sale_date': invoice.invoice_date,
                    'invoice_date': invoice.invoice_date,
                    'invoice_number': invoice.name or '',
                    'customer_address': self._format_address(company_partner),
                    'tobacco_sales_total': tobacco_total,
                    'cigar_quantity_sold': cigar_quantity_sold,  # Quantity sold
                    'cigar_stick_count': cigar_stick_count,     # Individual cigars sold
                    'cigar_sales_total': cigar_total,
                    # 'total_ounces': total_ounces,
                    'invoice_id': invoice.id,
                })
        
        return sales_data

    def _format_address(self, partner):
        """
        Format partner address into a single string.
        
        Args:
            partner: res.partner record
            
        Returns:
            str: Formatted address
        """
        address_parts = []
        if partner.street:
            address_parts.append(partner.street)
        if partner.street2:
            address_parts.append(partner.street2)
        if partner.city:
            address_parts.append(partner.city)
        if partner.state_id:
            address_parts.append(partner.state_id.name)
        if partner.zip:
            address_parts.append(partner.zip)
        if partner.country_id:
            address_parts.append(partner.country_id.name)
            
        return ', '.join(address_parts)

    def _generate_excel_file(self, sales_data):
        """
        Generate Excel file with tobacco sales data.

        Args:
            sales_data (list): List of sales data dictionaries

        Returns:
            bytes: Excel file content
        """
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        worksheet = workbook.add_worksheet('Tobacco Sales Report')

        # Set page setup for landscape orientation and proper printing
        worksheet.set_landscape()
        worksheet.set_paper(1)  # Letter size (8.5" x 11")
        worksheet.fit_to_pages(1, 0)  # Fit to 1 page wide, unlimited pages tall
        worksheet.set_margins(0.5, 0.5, 0.75, 0.75)  # left, right, top, bottom

        # Set print area to include all data (will be set after data is written)
        # worksheet.print_area() will be called later

        # Set header and footer for professional printing
        worksheet.set_header('&C&16&B' + f'{self.product_type.title()} Sales Report')
        worksheet.set_footer('&L&D &T&C&P&R' + f'Generated by {self.user_id.name if self.user_id else "System"}')

        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'font_size': 12,
            'bg_color': '#D7E4BC',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'text_wrap': True  # Enable word wrap for headers too
        })

        title_format = workbook.add_format({
            'bold': True,
            'font_size': 14,  # Slightly smaller for better fit
            'align': 'center',
            'valign': 'vcenter',
            'bg_color': '#F2F2F2',  # Light gray background
            'border': 1
        })

        data_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'top',
            'text_wrap': True  # Enable word wrap for all text data
        })

        # Special format for address fields with enhanced word wrap
        address_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'top',
            'text_wrap': True,
            'font_size': 9  # Slightly smaller font for addresses to fit more text
        })

        # Format for customer names with word wrap
        name_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'top',
            'text_wrap': True,
            'bold': False
        })

        number_format = workbook.add_format({
            'border': 1,
            'align': 'right',
            'valign': 'vcenter',
            'num_format': '#,##0.00'
        })

        date_format = workbook.add_format({
            'border': 1,
            'align': 'center',
            'valign': 'vcenter',
            'num_format': 'mm/dd/yyyy'
        })

        # Generate headers based on product type
        base_headers = [
            'Customer Name',
            'Federal ID',
            'Sale Date',
            'Invoice Date',
            'Invoice Number',
            'Customer Address'
        ]

        if self.product_type == 'tobacco':
            product_headers = [
                'Total Tobacco Cost',
                # 'Total Ounces'  # Commented out for now
            ]
        elif self.product_type == 'cigars':
            product_headers = [
                'Cigar Quantity',
                'Premium Cigar Stick Count',
                'Premium Cigar Cost'
            ]
        else:  # both
            product_headers = [
                'Total Tobacco Cost',
                'Cigar Quantity',
                'Premium Cigar Stick Count',
                'Premium Cigar Cost',
                # 'Total Ounces'  # Commented out for now
            ]

        headers = base_headers + product_headers

        # Set column widths optimized for landscape printing
        # Adjust widths based on total number of columns to fit better in landscape
        total_cols = len(headers)
        if total_cols <= 7:  # Tobacco only or cigars only
            base_widths = [22, 12, 11, 11, 14, 35]  # Customer Name, Federal ID, Sale Date, Invoice Date, Invoice Number, Customer Address
            product_widths = [14] * len(product_headers)  # Product columns
        else:  # Both tobacco and cigars (more columns)
            base_widths = [18, 10, 10, 10, 12, 28]  # Narrower for more columns
            product_widths = [12] * len(product_headers)  # Narrower product columns

        all_widths = base_widths + product_widths
        for i, width in enumerate(all_widths):
            col_letter = chr(ord('A') + i)
            worksheet.set_column(f'{col_letter}:{col_letter}', width)

        # Generate title based on product type
        product_type_title = {
            'both': 'Tobacco & Cigar Sales Report',
            'tobacco': 'Tobacco Sales Report',
            'cigars': 'Cigar Sales Report'
        }.get(self.product_type, 'Tobacco Sales Report')

        # Set row heights for better display
        worksheet.set_row(0, 25)  # Title row height
        worksheet.set_row(1, 5)   # Empty row height (spacer)
        worksheet.set_row(2, 20)  # Header row height

        # Write title with dynamic column range
        last_col = chr(ord('A') + len(headers) - 1)
        worksheet.merge_range(f'A1:{last_col}1', f'{product_type_title} ({self.date_from} to {self.date_to})', title_format)

        for col, header in enumerate(headers):
            worksheet.write(2, col, header, header_format)

        # Write data
        row = 3
        totals = {
            'tobacco_sales_total': 0,
            'cigar_sales_total': 0,
            'cigar_quantity_sold': 0,
            'cigar_stick_count': 0,
            # 'total_ounces': 0  # Commented out for now
        }

        for data in sales_data:
            col = 0
            # Write base data with appropriate formatting
            worksheet.write(row, col, data['customer_name'], name_format); col += 1  # Customer name with word wrap
            worksheet.write(row, col, data['federal_id'], data_format); col += 1     # Federal ID
            worksheet.write(row, col, data['sale_date'], date_format); col += 1     # Sale date
            worksheet.write(row, col, data['invoice_date'], date_format); col += 1  # Invoice date
            worksheet.write(row, col, data['invoice_number'], data_format); col += 1 # Invoice number
            worksheet.write(row, col, data['customer_address'], address_format); col += 1  # Address with enhanced word wrap

            # Write product-specific data based on type
            if self.product_type == 'tobacco':
                worksheet.write(row, col, data['tobacco_sales_total'], number_format); col += 1
                # worksheet.write(row, col, data['total_ounces'], number_format); col += 1  # Commented out for now
            elif self.product_type == 'cigars':
                worksheet.write(row, col, data['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, data['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, data['cigar_sales_total'], number_format); col += 1
            else:  # both
                worksheet.write(row, col, data['tobacco_sales_total'], number_format); col += 1
                worksheet.write(row, col, data['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, data['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, data['cigar_sales_total'], number_format); col += 1
                # worksheet.write(row, col, data['total_ounces'], number_format); col += 1  # Commented out for now

            # Add to totals
            for key in totals:
                totals[key] += data[key]

            # Set row height to accommodate word wrap (minimum 30 pixels for wrapped text)
            worksheet.set_row(row, 30)
            row += 1

        # Write totals row
        if sales_data:
            row += 1
            col = 5
            worksheet.write(row, col, 'TOTALS:', header_format)
            col += 1

            # Write totals based on product type
            if self.product_type == 'tobacco':
                worksheet.write(row, col, totals['tobacco_sales_total'], number_format); col += 1
                # worksheet.write(row, col, totals['total_ounces'], number_format); col += 1  # Commented out for now
            elif self.product_type == 'cigars':
                worksheet.write(row, col, totals['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_sales_total'], number_format); col += 1
            else:  # both
                worksheet.write(row, col, totals['tobacco_sales_total'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_quantity_sold'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_stick_count'], number_format); col += 1
                worksheet.write(row, col, totals['cigar_sales_total'], number_format); col += 1
                # worksheet.write(row, col, totals['total_ounces'], number_format); col += 1  # Commented out for now

        # Set print area to include all data
        if sales_data:
            last_col = chr(ord('A') + len(headers) - 1)
            last_row = row + 1 if sales_data else 3  # Include totals row if data exists
            worksheet.print_area(f'A1:{last_col}{last_row}')
        else:
            # If no data, just include headers
            last_col = chr(ord('A') + len(headers) - 1)
            worksheet.print_area(f'A1:{last_col}3')

        # Set print options for better header display
        worksheet.repeat_rows(0, 2)  # Repeat title and header rows on each page
        worksheet.set_h_pagebreaks([])  # Clear any automatic page breaks

        workbook.close()
        output.seek(0)
        return output.read()
