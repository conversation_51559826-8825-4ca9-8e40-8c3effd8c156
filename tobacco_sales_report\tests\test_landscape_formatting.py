# -*- coding: utf-8 -*-
"""
Test Landscape Formatting in Tobacco Sales Report

This test file verifies that the Excel report is properly formatted for landscape
printing with appropriate page setup, column widths, and header formatting.
"""

from odoo.tests.common import TransactionCase
from datetime import date
import base64
import io

try:
    import xlsxwriter
    from openpyxl import load_workbook
except ImportError:
    xlsxwriter = None


class TestTobaccoSalesReportLandscapeFormatting(TransactionCase):
    """Test landscape formatting functionality in tobacco sales reports."""

    def setUp(self):
        """Set up test data."""
        super().setUp()
        
        # Create test user
        self.test_user = self.env['res.users'].create({
            'name': 'Test User',
            'login': '<EMAIL>',
            'email': '<EMAIL>',
        })
        
        # Create test customer
        self.customer = self.env['res.partner'].create({
            'name': 'Test Customer',
            'vat': '123456789',
            'street': '123 Test St',
            'city': 'Test City',
            'state_id': self.env['res.country.state'].search([('code', '=', 'CA')], limit=1).id,
            'zip': '12345',
            'country_id': self.env['res.country'].search([('code', '=', 'US')], limit=1).id,
        })
        
        # Create test product categories
        self.tobacco_category = self.env['product.category'].create({
            'name': 'Tobacco Products',
        })
        
        self.cigar_category = self.env['product.category'].create({
            'name': 'Premium Cigars',
        })
        
        # Create test products
        self.tobacco_product = self.env['product.product'].create({
            'name': 'Test Tobacco Product',
            'categ_id': self.tobacco_category.id,
            'list_price': 100.0,
        })
        
        self.cigar_product = self.env['product.product'].create({
            'name': 'Test Cigar Product',
            'categ_id': self.cigar_category.id,
            'list_price': 50.0,
        })

    def test_landscape_page_setup(self):
        """Test that Excel report has proper landscape page setup."""
        if not xlsxwriter:
            self.skipTest("xlsxwriter not available")
            
        # Create report
        report = self.env['tobacco.sales.report'].create({
            'date_from': date.today(),
            'date_to': date.today(),
            'user_id': self.test_user.id,
            'product_type': 'both',
        })
        
        # Generate Excel file
        sales_data = []  # Empty data for testing formatting
        excel_content = report._generate_excel_file(sales_data)
        
        # Verify Excel file was generated
        self.assertTrue(excel_content)
        self.assertIsInstance(excel_content, bytes)

    def test_column_width_optimization(self):
        """Test that column widths are optimized for different report types."""
        if not xlsxwriter:
            self.skipTest("xlsxwriter not available")
            
        # Test tobacco only report (fewer columns)
        report_tobacco = self.env['tobacco.sales.report'].create({
            'date_from': date.today(),
            'date_to': date.today(),
            'user_id': self.test_user.id,
            'product_type': 'tobacco',
        })
        
        excel_tobacco = report_tobacco._generate_excel_file([])
        self.assertTrue(excel_tobacco)
        
        # Test both tobacco and cigars report (more columns)
        report_both = self.env['tobacco.sales.report'].create({
            'date_from': date.today(),
            'date_to': date.today(),
            'user_id': self.test_user.id,
            'product_type': 'both',
        })
        
        excel_both = report_both._generate_excel_file([])
        self.assertTrue(excel_both)

    def test_report_with_sample_data(self):
        """Test report generation with sample data to verify formatting."""
        if not xlsxwriter:
            self.skipTest("xlsxwriter not available")
            
        # Create sample invoice
        invoice = self.env['account.move'].with_user(self.test_user).create({
            'type': 'out_invoice',
            'partner_id': self.customer.id,
            'invoice_date': date.today(),
            'state': 'draft',
            'invoice_line_ids': [
                (0, 0, {
                    'product_id': self.tobacco_product.id,
                    'quantity': 1,
                    'price_unit': 100.0,
                }),
                (0, 0, {
                    'product_id': self.cigar_product.id,
                    'quantity': 2,
                    'price_unit': 50.0,
                }),
            ],
        })
        invoice.action_post()
        
        # Create report
        report = self.env['tobacco.sales.report'].create({
            'date_from': date.today(),
            'date_to': date.today(),
            'user_id': self.test_user.id,
            'product_type': 'both',
        })
        
        # Generate report
        action = report.generate_report()
        
        # Verify report was generated successfully
        self.assertEqual(action['type'], 'ir.actions.act_url')
        self.assertIn('download=true', action['url'])
        self.assertEqual(report.state, 'generated')
        self.assertTrue(report.report_file)
        self.assertTrue(report.report_filename)

    def test_header_footer_setup(self):
        """Test that header and footer are properly configured."""
        if not xlsxwriter:
            self.skipTest("xlsxwriter not available")
            
        # Create report
        report = self.env['tobacco.sales.report'].create({
            'date_from': date.today(),
            'date_to': date.today(),
            'user_id': self.test_user.id,
            'product_type': 'cigars',
        })
        
        # Generate Excel file
        excel_content = report._generate_excel_file([])
        
        # Verify Excel file was generated
        self.assertTrue(excel_content)
        
        # The header/footer setup is tested by ensuring no exceptions are raised
        # during Excel generation with the landscape formatting code
